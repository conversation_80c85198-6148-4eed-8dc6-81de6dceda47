const featureCategoryValues = [
  'safety_and_security',
  'comfort',
  'exterior',
  'entertainment',
  'other',
] as const

export const FeatureCategory = featureCategoryValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof featureCategoryValues)[number],
    (typeof featureCategoryValues)[number]
  >,
)

export const LocalizedFeatureCategory = featureCategoryValues.reduce(
  (acc, value) => {
    const translations: Record<string, string> = {
      safety_and_security: 'الأمان',
      comfort: 'الراحة',
      exterior: 'التصميم الخارجي',
      entertainment: 'الترفيه',
      other: 'أخرى',
    }
    acc[value] = translations[value]
    return acc
  },
  {} as Record<(typeof featureCategoryValues)[number], string>,
)

export type FeatureCategoryType = (typeof featureCategoryValues)[number]
