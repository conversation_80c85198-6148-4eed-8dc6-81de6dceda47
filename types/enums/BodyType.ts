import { useI18n } from 'vue-i18n'

const bodyTypeValues = [
  'suv',
  'coupe',
  'sedan',
  'crossover',
  'hard_top_convertable',
  'soft_top_convertable',
  'pick_up_truck',
  'hatchback',
  'sports_car',
  'van',
  'mini_van',
  'wagon',
  'other',
] as const

export const BodyType = bodyTypeValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof bodyTypeValues)[number],
    (typeof bodyTypeValues)[number]
  >,
)

export const useLocalizedBodyType = () => {
  const { t } = useI18n()
  return bodyTypeValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.body_type.${value}`)
      return acc
    },
    {} as Record<(typeof bodyTypeValues)[number], string>,
  )
}

export type BodyTypeType = (typeof bodyTypeValues)[number]
