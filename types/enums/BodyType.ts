const bodyTypeValues = [
  'suv',
  'coupe',
  'sedan',
  'crossover',
  'hard_top_convertable',
  'soft_top_convertable',
  'pick_up_truck',
  'hatchback',
  'sports_car',
  'van',
  'mini_van',
  'wagon',
  'other'
] as const;

export const BodyType = bodyTypeValues.reduce((acc, value) => {
  acc[value] = value;
  return acc;
}, {} as Record<typeof bodyTypeValues[number], typeof bodyTypeValues[number]>);

export const LocalizedBodyType = bodyTypeValues.reduce((acc, value) => {
  const translations: Record<string, string> = {
    'suv': 'جيب | SUV',
    'coupe': 'كوبيه | Coupe',
    'sedan': 'سيدان | Sedan',
    'crossover': 'كروس أوفر | Crossover',
    'hard_top_convertable': 'مكشوفة بسقف صلب | Hard Top Convertible',
    'soft_top_convertable': 'مكشوفة بسقف لين | Soft Top Convertible',
    'pick_up_truck': 'شاحنة بيك آب | Pick-up Truck',
    'hatchback': 'هاتشباك | Hatchback',
    'sports_car': 'سيارة رياضية | Sports Car',
    'van': 'فان | Van',
    'mini_van': 'ميني فان | Mini Van',
    'wagon': 'ستيشن | Wagon',
    'other': 'أخرى | Other'
  };
  acc[value] = translations[value];
  return acc;
}, {} as Record<typeof bodyTypeValues[number], string>);

export type BodyTypeType = typeof bodyTypeValues[number];
