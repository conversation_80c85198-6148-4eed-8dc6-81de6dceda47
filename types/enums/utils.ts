import { useI18n } from 'vue-i18n'

/**
 * Generic helper to get localized enum value
 * @param enumKey - The enum key in the i18n file (e.g., 'city', 'status')
 * @param value - The enum value to translate
 * @returns Localized string
 */
export const useLocalizedEnumValue = <T extends string>(
  enumKey: string,
  value: T
): string => {
  const { t } = useI18n()
  return t(`enums.${enumKey}.${value}`)
}

/**
 * Generic helper to get all localized enum values
 * @param enumKey - The enum key in the i18n file (e.g., 'city', 'status')
 * @param values - Array of enum values
 * @returns Object mapping enum values to localized strings
 */
export const useLocalizedEnum = <T extends readonly string[]>(
  enumKey: string,
  values: T
): Record<T[number], string> => {
  const { t } = useI18n()
  return values.reduce(
    (acc, value) => {
      acc[value] = t(`enums.${enumKey}.${value}`)
      return acc
    },
    {} as Record<T[number], string>
  )
}
