import { useI18n } from 'vue-i18n'

const statusValues = ['new', 'used'] as const

export const useLocalizedStatus = () => {
  const { t } = useI18n()
  return statusValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.status.${value}`)
      return acc
    },
    {} as Record<(typeof statusValues)[number], string>,
  )
}

export const useLocalizedStatusValue = (value: Status) => {
  const { t } = useI18n()
  return t(`enums.status.${value}`)
}

export type Status = (typeof statusValues)[number]
