const statusValues = ['new', 'used'] as const

export const Status = statusValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<(typeof statusValues)[number], (typeof statusValues)[number]>,
)

export const getLocalizedStatus = ($t: (key: string) => string) =>
  statusValues.reduce(
    (acc, value) => {
      acc[value] = $t(`enums.status.${value}`)
      return acc
    },
    {} as Record<(typeof statusValues)[number], string>,
  )

export type StatusType = (typeof statusValues)[number]
