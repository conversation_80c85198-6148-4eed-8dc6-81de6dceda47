const statusValues = ['new', 'used'] as const

export const Status = statusValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<(typeof statusValues)[number], (typeof statusValues)[number]>,
)

export const LocalizedStatus = statusValues.reduce(
  (acc, value) => {
    const translations: Record<string, string> = {
      new: 'جديد',
      used: 'مستعمل',
    }
    acc[value] = translations[value]
    return acc
  },
  {} as Record<(typeof statusValues)[number], string>,
)

export type StatusType = (typeof statusValues)[number]
