import { useI18n } from 'vue-i18n'

const statusValues = ['new', 'used'] as const

export const Status = statusValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<(typeof statusValues)[number], (typeof statusValues)[number]>,
)

export const useLocalizedStatus = () => {
  const { t } = useI18n()
  return statusValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.status.${value}`)
      return acc
    },
    {} as Record<(typeof statusValues)[number], string>,
  )
}

export type StatusType = (typeof statusValues)[number]
