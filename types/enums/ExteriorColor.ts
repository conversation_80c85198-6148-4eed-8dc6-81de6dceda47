const exteriorColorValues = [
  'white',
  'black',
  'gray',
  'silver',
  'red',
  'blue',
  'yellow',
  'green',
  'beige',
  'brown',
  'orange',
  'gold',
] as const

export const ExteriorColor = exteriorColorValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof exteriorColorValues)[number],
    (typeof exteriorColorValues)[number]
  >,
)

export const getLocalizedExteriorColor = ($t: (key: string) => string) =>
  exteriorColorValues.reduce(
    (acc, value) => {
      acc[value] = $t(`enums.exterior_color.${value}`)
      return acc
    },
    {} as Record<(typeof exteriorColorValues)[number], string>,
  )

export type ExteriorColorType = (typeof exteriorColorValues)[number]
