const exteriorColorValues = [
  'white',
  'black',
  'gray',
  'silver',
  'red',
  'blue',
  'yellow',
  'green',
  'beige',
  'brown',
  'orange',
  'gold',
] as const

export const ExteriorColor = exteriorColorValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof exteriorColorValues)[number],
    (typeof exteriorColorValues)[number]
  >,
)

export const LocalizedExteriorColor = exteriorColorValues.reduce(
  (acc, value) => {
    const translations: Record<string, string> = {
      white: 'أبيض',
      black: 'أسود',
      gray: 'رمادي',
      silver: 'فضي',
      red: 'أحمر',
      blue: 'أزرق',
      yellow: 'أصفر',
      green: 'أخضر',
      beige: 'بيج',
      brown: 'بني',
      orange: 'برتقالي',
      gold: 'ذهبي',
    }
    acc[value] = translations[value]
    return acc
  },
  {} as Record<(typeof exteriorColorValues)[number], string>,
)

export type ExteriorColorType = (typeof exteriorColorValues)[number]
