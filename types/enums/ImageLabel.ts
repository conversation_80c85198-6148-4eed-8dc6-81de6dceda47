const imageLabelValues = [
  'front_angle',
  'rear_angle',
  'engine',
  'exterior_roof',
  'interior_front',
  'odometer',
  'transmission',
  'tire',
  'stereo',
  'rear_seats',
  'trunk_view',
  'front_mirror',
  'steering',
  'dashboard',
  'features',
  'interior_roof',
  'front_ac',
  'rear_ac',
  'keys',
  'side_view',
  'rear_view',
  'doors',
  'fingerprint',
  'exhaust',
  'trunk_fuel_buttons',
] as const

export const ImageLabel = imageLabelValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof imageLabelValues)[number],
    (typeof imageLabelValues)[number]
  >,
)

export const LocalizedImageLabel = imageLabelValues.reduce(
  (acc, value) => {
    const translations: Record<string, string> = {
      front_angle: 'زاوية أمامية',
      rear_angle: 'زاوية خلفية',
      engine: 'محرك',
      exterior_roof: 'سقف خارجي',
      interior_front: 'زاوية داخلية أمامية',
      odometer: 'كيلومتراج',
      transmission: 'الغيار',
      tire: 'دولاب',
      stereo: 'المسجلة',
      rear_seats: 'كراسي ورانية',
      trunk_view: 'لقطة من الصندوق',
      front_mirror: 'لقطة من المراية ألأمامية',
      steering: 'الدركسيون',
      dashboard: 'داش بورد كامل',
      features: 'مميزات',
      interior_roof: 'سقف داخلي',
      front_ac: 'مكيف أمامي',
      rear_ac: 'مكيف خلفي',
      keys: 'المفاتيح',
      side_view: 'جانبية',
      rear_view: 'خلفية',
      doors: 'أبواب',
      fingerprint: 'بصمة',
      exhaust: 'الطبون',
      trunk_fuel_buttons: 'كبسة فتح الطبون و البانزين',
    }
    acc[value] = translations[value]
    return acc
  },
  {} as Record<(typeof imageLabelValues)[number], string>,
)

export type ImageLabelType = (typeof imageLabelValues)[number]
