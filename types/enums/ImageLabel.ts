import { useI18n } from 'vue-i18n'

const imageLabelValues = [
  'front_angle',
  'rear_angle',
  'engine',
  'exterior_roof',
  'interior_front',
  'odometer',
  'transmission',
  'tire',
  'stereo',
  'rear_seats',
  'trunk_view',
  'front_mirror',
  'steering',
  'dashboard',
  'features',
  'interior_roof',
  'front_ac',
  'rear_ac',
  'keys',
  'side_view',
  'rear_view',
  'doors',
  'fingerprint',
  'exhaust',
  'trunk_fuel_buttons',
] as const

export const ImageLabel = imageLabelValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof imageLabelValues)[number],
    (typeof imageLabelValues)[number]
  >,
)

export const useLocalizedImageLabel = () => {
  const { t } = useI18n()
  return imageLabelValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.image_label.${value}`)
      return acc
    },
    {} as Record<(typeof imageLabelValues)[number], string>,
  )
}

export type ImageLabelType = (typeof imageLabelValues)[number]
