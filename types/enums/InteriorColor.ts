const interiorColorValues = [
  'black',
  'gray',
  'beige',
  'white',
  'brown',
  'tan',
  'ivory',
  'blue',
  'red',
  'two_tone',
  'custom_design',
] as const

export const InteriorColor = interiorColorValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof interiorColorValues)[number],
    (typeof interiorColorValues)[number]
  >,
)

export const LocalizedInteriorColor = interiorColorValues.reduce(
  (acc, value) => {
    const translations: Record<string, string> = {
      black: 'أسود',
      gray: 'رمادي',
      beige: 'بيج',
      white: 'أبيض',
      brown: 'بني',
      tan: 'أسمر فاتح',
      ivory: 'عاجي',
      blue: 'أزرق',
      red: 'أحمر',
      two_tone: 'لونين',
      custom_design: 'تصميم خاص',
    }
    acc[value] = translations[value]
    return acc
  },
  {} as Record<(typeof interiorColorValues)[number], string>,
)

export type InteriorColorType = (typeof interiorColorValues)[number]
