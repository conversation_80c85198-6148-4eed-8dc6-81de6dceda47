const interiorColorValues = [
  'black',
  'gray',
  'beige',
  'white',
  'brown',
  'tan',
  'ivory',
  'blue',
  'red',
  'two_tone',
  'custom_design',
] as const

export const InteriorColor = interiorColorValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof interiorColorValues)[number],
    (typeof interiorColorValues)[number]
  >,
)

export const getLocalizedInteriorColor = ($t: (key: string) => string) =>
  interiorColorValues.reduce(
    (acc, value) => {
      acc[value] = $t(`enums.interior_color.${value}`)
      return acc
    },
    {} as Record<(typeof interiorColorValues)[number], string>,
  )

export type InteriorColorType = (typeof interiorColorValues)[number]
