const drivetrainSystemValues = ['fwd', 'rwd', '4wd', 'awd'] as const

export const DrivetrainSystem = drivetrainSystemValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof drivetrainSystemValues)[number],
    (typeof drivetrainSystemValues)[number]
  >,
)

export const LocalizedDrivetrainSystem = drivetrainSystemValues.reduce(
  (acc, value) => {
    const translations: Record<string, string> = {
      fwd: 'دفع أمامي | FWD',
      rwd: 'دفع خلفي | RWD',
      '4wd': 'دفع رباعي | 4WD',
      awd: 'دفع كلي | AWD',
    }
    acc[value] = translations[value]
    return acc
  },
  {} as Record<(typeof drivetrainSystemValues)[number], string>,
)

export type DrivetrainSystemType = (typeof drivetrainSystemValues)[number]
