const drivetrainSystemValues = ['fwd', 'rwd', '4wd', 'awd'] as const

export const DrivetrainSystem = drivetrainSystemValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof drivetrainSystemValues)[number],
    (typeof drivetrainSystemValues)[number]
  >,
)

export const getLocalizedDrivetrainSystem = ($t: (key: string) => string) =>
  drivetrainSystemValues.reduce(
    (acc, value) => {
      acc[value] = $t(`enums.drivetrain_system.${value}`)
      return acc
    },
    {} as Record<(typeof drivetrainSystemValues)[number], string>,
  )

export type DrivetrainSystemType = (typeof drivetrainSystemValues)[number]
