import { useI18n } from 'vue-i18n'

const drivetrainSystemValues = ['fwd', 'rwd', '4wd', 'awd'] as const

export const DrivetrainSystem = drivetrainSystemValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof drivetrainSystemValues)[number],
    (typeof drivetrainSystemValues)[number]
  >,
)

export const useLocalizedDrivetrainSystem = () => {
  const { t } = useI18n()
  return drivetrainSystemValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.drivetrain_system.${value}`)
      return acc
    },
    {} as Record<(typeof drivetrainSystemValues)[number], string>,
  )
}

export type DrivetrainSystemType = (typeof drivetrainSystemValues)[number]
