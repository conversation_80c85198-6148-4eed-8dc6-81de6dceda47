const currencyValues = ['syp', 'usd', 'try'] as const

export const Currency = currencyValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof currencyValues)[number],
    (typeof currencyValues)[number]
  >,
)

export const LocalizedCurrency = currencyValues.reduce(
  (acc, value) => {
    const translations: Record<string, string> = {
      syp: 'ليرة سوري',
      usd: 'دولار أمريكي',
      try: 'ليرة تركي',
    }
    acc[value] = translations[value]
    return acc
  },
  {} as Record<(typeof currencyValues)[number], string>,
)

export type CurrencyType = (typeof currencyValues)[number]
