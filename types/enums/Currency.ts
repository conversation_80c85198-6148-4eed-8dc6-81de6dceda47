import { useI18n } from 'vue-i18n'

const currencyValues = ['syp', 'usd', 'try'] as const

export const Currency = currencyValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof currencyValues)[number],
    (typeof currencyValues)[number]
  >,
)

export const useLocalizedCurrency = () => {
  const { t } = useI18n()
  return currencyValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.currency.${value}`)
      return acc
    },
    {} as Record<(typeof currencyValues)[number], string>,
  )
}

export type CurrencyType = (typeof currencyValues)[number]
