const fuelTypeValues = ['petrol', 'diesel', 'electric', 'hybrid'] as const

export const FuelType = fuelTypeValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof fuelTypeValues)[number],
    (typeof fuelTypeValues)[number]
  >,
)

export const getLocalizedFuelType = ($t: (key: string) => string) =>
  fuelTypeValues.reduce(
    (acc, value) => {
      acc[value] = $t(`enums.fuel_type.${value}`)
      return acc
    },
    {} as Record<(typeof fuelTypeValues)[number], string>,
  )

export type FuelTypeType = (typeof fuelTypeValues)[number]
