const fuelTypeValues = ['petrol', 'diesel', 'electric', 'hybrid'] as const

export const FuelType = fuelTypeValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof fuelTypeValues)[number],
    (typeof fuelTypeValues)[number]
  >,
)

export const LocalizedFuelType = fuelTypeValues.reduce(
  (acc, value) => {
    const translations: Record<string, string> = {
      petrol: 'بنزين',
      diesel: 'ديزل',
      electric: 'كهربائي',
      hybrid: 'هجين',
    }
    acc[value] = translations[value]
    return acc
  },
  {} as Record<(typeof fuelTypeValues)[number], string>,
)

export type FuelTypeType = (typeof fuelTypeValues)[number]
