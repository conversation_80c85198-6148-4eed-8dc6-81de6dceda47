const regionalSpecificationValues = [
  'gcc',
  'american',
  'canadian',
  'european',
  'japanese',
  'korean',
  'chinese',
  'other',
] as const

export const RegionalSpecification = regionalSpecificationValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof regionalSpecificationValues)[number],
    (typeof regionalSpecificationValues)[number]
  >,
)

export const LocalizedRegionalSpecification =
  regionalSpecificationValues.reduce(
    (acc, value) => {
      const translations: Record<string, string> = {
        gcc: 'خليجية',
        american: 'أمريكية',
        canadian: 'كندية',
        european: 'أوروبية',
        japanese: 'يابانية',
        korean: 'كورية',
        chinese: 'صينية',
        other: 'أخرى',
      }
      acc[value] = translations[value]
      return acc
    },
    {} as Record<(typeof regionalSpecificationValues)[number], string>,
  )

export type RegionalSpecificationType =
  (typeof regionalSpecificationValues)[number]
