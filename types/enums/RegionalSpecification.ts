import { useI18n } from 'vue-i18n'

const regionalSpecificationValues = [
  'gcc',
  'american',
  'canadian',
  'european',
  'japanese',
  'korean',
  'chinese',
  'other',
] as const

export const RegionalSpecification = regionalSpecificationValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof regionalSpecificationValues)[number],
    (typeof regionalSpecificationValues)[number]
  >,
)

export const useLocalizedRegionalSpecification = () => {
  const { t } = useI18n()
  return regionalSpecificationValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.regional_specification.${value}`)
      return acc
    },
    {} as Record<(typeof regionalSpecificationValues)[number], string>,
  )
}

export type RegionalSpecificationType =
  (typeof regionalSpecificationValues)[number]
