const transmissionTypeValues = [
  'automatic',
  'manual',
  'tiptronic',
  'cvt',
  'dct',
] as const

export const TransmissionType = transmissionTypeValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof transmissionTypeValues)[number],
    (typeof transmissionTypeValues)[number]
  >,
)

export const LocalizedTransmissionType = transmissionTypeValues.reduce(
  (acc, value) => {
    const translations: Record<string, string> = {
      automatic: 'أوتوماتيك',
      manual: 'يدوي',
      tiptronic: 'تيبترونيك | Tiptronic',
      cvt: 'ناقل حركة متغير باستمرار | CVT',
      dct: 'ناقل حركة مزدوج القابض | DCT',
    }
    acc[value] = translations[value]
    return acc
  },
  {} as Record<(typeof transmissionTypeValues)[number], string>,
)

export type TransmissionTypeType = (typeof transmissionTypeValues)[number]
