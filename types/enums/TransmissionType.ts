const transmissionTypeValues = [
  'automatic',
  'manual',
  'tiptronic',
  'cvt',
  'dct',
] as const

export const TransmissionType = transmissionTypeValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<
    (typeof transmissionTypeValues)[number],
    (typeof transmissionTypeValues)[number]
  >,
)

export const getLocalizedTransmissionType = ($t: (key: string) => string) =>
  transmissionTypeValues.reduce(
    (acc, value) => {
      acc[value] = $t(`enums.transmission_type.${value}`)
      return acc
    },
    {} as Record<(typeof transmissionTypeValues)[number], string>,
  )

export type TransmissionTypeType = (typeof transmissionTypeValues)[number]
