import { useLocalizedEnum, useLocalizedEnumValue } from './utils'

const cityValues = [
  'damascus',
  'aleppo',
  'homs',
  'hama',
  'latakia',
  'tartus',
  'idlib',
  'deir-ez-zor',
  'raqqa',
  'hasakah',
  'suwayda',
  'daraa',
  'quneitra',
  'rif-dimashq',
] as const

export const useLocalizedCity = () => useLocalizedEnum('city', cityValues)

export const useLocalizedCityValue = (value: City) =>
  useLocalizedEnumValue('city', value)

export type City = (typeof cityValues)[number]
