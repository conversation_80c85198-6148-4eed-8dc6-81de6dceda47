const cityValues = [
  'damascus',
  'aleppo',
  'homs',
  'hama',
  'latakia',
  'tartus',
  'idlib',
  'deir-ez-zor',
  'raqqa',
  'hasakah',
  'suwayda',
  'daraa',
  'quneitra',
  'rif-dimashq',
] as const

export const City = cityValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<(typeof cityValues)[number], (typeof cityValues)[number]>,
)

export const LocalizedCity = cityValues.reduce(
  (acc, value) => {
    const translations: Record<string, string> = {
      damascus: 'دمشق',
      aleppo: 'حلب',
      homs: 'حمص',
      hama: 'حماة',
      latakia: 'اللاذقية',
      tartus: 'طرطوس',
      idlib: 'إدلب',
      'deir-ez-zor': 'دير الزور',
      raqqa: 'الرقة',
      hasakah: 'الحسكة',
      suwayda: 'السويداء',
      daraa: 'درعا',
      quneitra: 'القنيطرة',
      'rif-dimashq': 'ريف دمشق',
    }
    acc[value] = translations[value]
    return acc
  },
  {} as Record<(typeof cityValues)[number], string>,
)

export type CityType = (typeof cityValues)[number]
