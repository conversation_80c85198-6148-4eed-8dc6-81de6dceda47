const cityValues = [
  'damascus',
  'aleppo',
  'homs',
  'hama',
  'latakia',
  'tartus',
  'idlib',
  'deir-ez-zor',
  'raqqa',
  'hasakah',
  'suwayda',
  'daraa',
  'quneitra',
  'rif-dimashq',
] as const

export const City = cityValues.reduce(
  (acc, value) => {
    acc[value] = value
    return acc
  },
  {} as Record<(typeof cityValues)[number], (typeof cityValues)[number]>,
)

export const useLocalizedCity = () => {
  const { t } = useI18n()
  return cityValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.city.${value}`)
      return acc
    },
    {} as Record<(typeof cityValues)[number], string>,
  )
}

export type CityType = (typeof cityValues)[number]
