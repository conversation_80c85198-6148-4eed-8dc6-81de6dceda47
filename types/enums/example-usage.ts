// Example usage of the enum files with useI18n composables
import {
  City,
  useLocalizedCity,
  CityType,
  BodyType,
  useLocalizedBodyType,
  BodyTypeType
} from './index'

// Example in a Vue component
export function useEnumExample() {
  // Get the enum values
  const selectedCity: CityType = City.damascus // 'damascus'
  const selectedBodyType: BodyTypeType = BodyType.suv // 'suv'

  // Get localized versions using composables
  const localizedCities = useLocalizedCity()
  const localizedBodyTypes = useLocalizedBodyType()

  // Use the localized text
  const cityName = localizedCities[selectedCity] // 'دمشق'
  const bodyTypeName = localizedBodyTypes[selectedBodyType] // 'جيب | SUV'

  return {
    selectedCity,
    selectedBodyType,
    cityName,
    bodyTypeName,
    localizedCities,
    localizedBodyTypes
  }
}

// Example for creating select options
export function useCityOptions() {
  const localizedCities = useLocalizedCity()

  return Object.entries(localizedCities).map(([value, label]) => ({
    value,
    label
  }))
}
