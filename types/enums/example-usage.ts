// Example usage of the enum files with $t function
import { useI18n } from 'vue-i18n'
import { 
  City, 
  getLocalizedCity, 
  CityType,
  BodyType,
  getLocalizedBodyType,
  BodyTypeType 
} from './index'

// Example in a Vue component
export function useEnumExample() {
  const { t } = useI18n()

  // Get the enum values
  const selectedCity: CityType = City.damascus // 'damascus'
  const selectedBodyType: BodyTypeType = BodyType.suv // 'suv'

  // Get localized versions
  const localizedCities = getLocalizedCity(t)
  const localizedBodyTypes = getLocalizedBodyType(t)

  // Use the localized text
  const cityName = localizedCities[selectedCity] // 'دمشق'
  const bodyTypeName = localizedBodyTypes[selectedBodyType] // 'جيب | SUV'

  return {
    selectedCity,
    selectedBodyType,
    cityName,
    bodyTypeName,
    localizedCities,
    localizedBodyTypes
  }
}

// Example for creating select options
export function getCityOptions(t: (key: string) => string) {
  const localizedCities = getLocalizedCity(t)
  
  return Object.entries(localizedCities).map(([value, label]) => ({
    value,
    label
  }))
}
